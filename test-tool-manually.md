# Manual Testing Guide for Deep Agent Language Model Tool

## 🚀 Quick Test Steps

### 1. Start Extension Development Host
```bash
# In VS Code, press F5 or use "Run Extension" from Run and Debug panel
# This opens a new VS Code window with the extension loaded
```

### 2. Verify Extension Activation
- Look for notification: "🤖 Deep Agent tool activated! Use in Agent/Edit mode or reference with #deepagent."
- Check Developer Console (Help > Toggle Developer Tools) for:
  ```
  🚀 Multi-Agent Orchestrator: Extension activating...
  🔧 Registering Deep Agent as Language Model Tool...
  ✅ Language Model Tool registered successfully
  📋 Tool name: deepagent
  ```

### 3. Test in Agent Mode
1. **Open Chat Panel**: `Ctrl+Shift+I` or View > Chat
2. **Switch to Agent Mode**: Click the mode selector and choose "Agent"
3. **Reference the Tool**: Type `#deepagent` and you should see it appear in autocomplete
4. **Test Simple Task**: 
   ```
   #deepagent Create a simple hello.txt file with "Hello World" content
   ```

### 4. Expected Results

#### ✅ Success Indicators:
- <PERSON><PERSON> appears in autocomplete when typing `#deepagent`
- Tool executes without "Invalid stream" errors
- Console shows:
  ```
  🔧 Deep Agent Tool: invoke called
  📋 Input: {task: "Create a simple hello.txt file with..."}
  🔑 Has toolInvocationToken: true
  🔑 Token type: object
  📝 Phase 1: Creating execution plan...
  ✅ Opus model obtained: claude-opus-4
  📋 Plan created: [...]
  🔄 Executing step 1: ...
  🛠️ Executing tool: copilot_createFile with input: ...
  ✅ Tool copilot_createFile executed successfully
  ```
- File `hello.txt` is created in workspace with correct content

#### ❌ Failure Indicators:
- "Invalid stream" error when invoking tools
- Tool not appearing in autocomplete
- Extension activation errors
- No `toolInvocationToken` received (shows `false` in logs)

### 5. Advanced Test Cases

#### Test Case 1: File Creation
```
#deepagent Create a TypeScript function that validates email addresses and save it to utils/email-validator.ts
```

#### Test Case 2: Multiple Files
```
#deepagent Create a simple Node.js project structure with package.json, index.js, and README.md
```

#### Test Case 3: Code Editing
```
#deepagent Read the package.json file and add a new script called "test" that runs "jest"
```

## 🔍 Debugging Tips

### Check Extension Logs
1. Open Developer Console: `Help > Toggle Developer Tools`
2. Look for Deep Agent related logs
3. Check for any error messages or stack traces

### Verify Tool Registration
```javascript
// In Developer Console, check if tool is registered:
vscode.lm.tools.find(t => t.name === 'deepagent')
```

### Check Available Tools
```javascript
// List all available tools:
vscode.lm.tools.map(t => ({name: t.name, description: t.description}))
```

## 🎯 Key Success Metrics

1. **Tool Registration**: ✅ Extension registers without errors
2. **Tool Discovery**: ✅ Tool appears in Agent mode autocomplete
3. **Token Access**: ✅ Tool receives valid `toolInvocationToken`
4. **Tool Invocation**: ✅ Can successfully call `copilot_createFile` and other tools
5. **File Operations**: ✅ Files are actually created/modified as requested
6. **Error Handling**: ✅ No "Invalid stream" errors

## 📊 Comparison with Previous Implementation

| Aspect | Chat Participant (Old) | Language Model Tool (New) |
|--------|----------------------|---------------------------|
| Tool Access | ❌ "Invalid stream" error | ✅ Full tool access |
| Token Context | ❌ No proper token | ✅ Valid `toolInvocationToken` |
| Process Context | ❌ Separate context | ✅ Same process as Agent |
| File Operations | ❌ Required workarounds | ✅ Direct tool invocation |
| User Experience | ❌ @deepagent in chat | ✅ #deepagent in Agent mode |

This new approach should completely resolve the "Invalid stream" error that was preventing the extension from working properly.
