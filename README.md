# Deep Agent - Multi-Agent Orchestrator

A VS Code Language Model Tool that implements a two-phase **Plan-and-Execute** workflow using Claude models. Designed for seamless integration with VS Code Agent/Edit mode.

## Overview

Deep Agent is now implemented as a **Language Model Tool** that can be invoked in VS Code Agent/Edit mode using `#deepagent`. This approach provides:

### Language Model Tool (`#deepagent`)
- Use `#deepagent` in VS Code Agent/Edit mode
- Full access to VS Code's tool ecosystem without "Invalid stream" errors
- Runs in the same process context as Agent/Edit sessions
- Proper `toolInvocationToken` access for seamless tool integration

### Key Advantages
- ✅ **No "Invalid stream" errors** - Resolved the critical tool invocation issue
- ✅ **Same-process execution** - Runs in Agent/Edit session context
- ✅ **Full tool access** - Can invoke `copilot_createFile`, `copilot_editFile`, etc.
- ✅ **Proper token context** - Receives valid `toolInvocationToken` from sessions

The core workflow remains the same:

1. **Phase 1 (Plan):** Uses Claude 4 Opus to decompose user prompts into numbered steps
2. **Phase 2 (Execute):** Iterates through each step using Claude 4 Sonnet with full VS Code tool access

## Features

### Language Model Tool Features
- **#deepagent** tool for multi-step task orchestration in Agent/Edit mode
- Seamless integration with VS Code's built-in tools and capabilities
- No "Invalid stream" errors - full tool invocation support
- Proper session context and token management

### Core Capabilities
- **Multi-agent orchestration** with Claude 4 Opus (planning) and Claude 4 Sonnet (execution)
- **Full tool integration** with VS Code's built-in tools:
  - File system operations (`copilot_createFile`, `copilot_editFile`, `copilot_readFile`)
  - Workspace search and symbol search
  - Terminal and task execution
- **Streaming execution** with real-time feedback
- **Built-in safety** through VS Code's confirmation UI for destructive actions
- **Session-aware execution** with proper token context

## Requirements

- VS Code 1.85.0 or higher
- GitHub Copilot or other language model provider configured
- Claude 4 models (Opus and Sonnet) available through your language model provider

## Usage

### Language Model Tool (Agent/Edit Mode) - **RECOMMENDED**

1. Open the Copilot Chat view (⌥⇧⌘I / Alt-Shift-Cmd-I)
2. Switch to **Agent mode** or **Edit mode** (not Ask mode)
3. Type `#deepagent_lmt` to reference the tool, followed by your request
4. Watch as the extension plans and executes your task step by step

**Example:**
```
#deepagent_lmt Create a TypeScript function that validates email addresses and save it to utils/email-validator.ts
```

### Key Benefits of Language Model Tool Approach

- ✅ **No "Invalid stream" errors** - Resolved the critical issue that prevented tool usage
- ✅ **Full tool access** - Can successfully invoke `copilot_createFile`, `copilot_editFile`, etc.
- ✅ **Same-process execution** - Runs in the same context as Agent/Edit sessions
- ✅ **Proper token management** - Receives valid `toolInvocationToken` for seamless integration

### Legacy Chat Participant Mode (Deprecated)

> **Note:** The chat participant approach (`@deepagent`) has been deprecated due to "Invalid stream" errors when invoking tools. Use the Language Model Tool approach above instead.

**Example (deprecated):**
```
@deepagent Add a new command to export data as JSON and include comprehensive tests
```

### Workflow

The Language Model Tool follows this process:
1. **Planning Phase:** Uses Claude 4 Opus to decompose the task into numbered steps
2. **Execution Phase:** Iterates through each step using Claude 4 Sonnet with full VS Code tool access
3. **Tool Integration:** Successfully invokes `copilot_createFile`, `copilot_editFile`, and other VS Code tools
4. **Real-time Feedback:** Streams results back with detailed progress information

## Installation

### Basic Setup (Chat Participant Only)
1. Clone this repository
2. Run `npm install`
3. Run `npm run compile`
4. Press F5 to launch a new VS Code window with the extension loaded
5. Open the Copilot Chat view and try `@deepagent`

### Full Setup (Chat Participant + MCP Server)
1. Follow steps 1-4 above
2. Configure the MCP server following the [MCP Setup Guide](MCP_SETUP.md)
3. Use `@deepagent` in Ask mode OR use Deep Agent tools in Agent/Edit mode

## Development

### Building

```bash
npm install
npm run compile
```

### Package Structure

```
src/
├── extension.ts     # Main orchestrator and activation
├── models.ts        # Claude model selection helper
├── planner.ts       # Plan generation with Opus
├── executor.ts      # Step execution with Sonnet
├── mcp-server.ts    # MCP server implementation
└── mcp-launcher.ts  # Standalone MCP server launcher
```

## Architecture

The extension follows a simple orchestration pattern:

```
User → @deepagent → Opus (plan) → for each step { Sonnet (execute) } → stream results
```

- **Planning**: Opus receives the user prompt and generates a numbered action plan
- **Execution**: Each step is sent to Sonnet running in agent mode with access to VS Code's Language Model Tools
- **Streaming**: All responses are streamed back through the chat interface for real-time feedback

## Safety

File writes and shell commands automatically trigger VS Code's built-in confirmation dialogs, ensuring user control over destructive actions.

## License

MIT