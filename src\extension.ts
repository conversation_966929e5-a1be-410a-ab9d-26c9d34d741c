import * as vscode from 'vscode';
import { getModel } from './models';
import { createPlan } from './planner';
import { DeepAgentMCPServer } from './mcp-server';
import { VSCodeIPCBridge } from './ipc-bridge';
import { registerSessionIntegrationCommands } from './session-integration-example';

let mcpServer: DeepAgentMCPServer | undefined;
let ipcBridge: VSCodeIPCBridge | undefined;

export function activate(ctx: vscode.ExtensionContext): void {
  console.log('🚀 Multi-Agent Orchestrator: Extension activating...');

  try {
    // Register a test command first
    const testCommand = vscode.commands.registerCommand('deepagent.test', () => {
      vscode.window.showInformationMessage('Deep Agent extension is active!');
      console.log('🧪 Test command executed - extension is active');
    });
    ctx.subscriptions.push(testCommand);
    
    // Register the chat participant with explicit error handling
    console.log('📝 Registering chat participant with ID: deepagent');
    
    try {
      const participant = vscode.chat.createChatParticipant('deepagent', handler);
      ctx.subscriptions.push(participant);
      
      console.log('✅ Chat participant created successfully');
      console.log('📋 Participant object:', typeof participant);
      
    } catch (participantError) {
      console.error('❌ Failed to create chat participant:', participantError);
      throw participantError;
    }
    
    console.log('✅ Multi-Agent Orchestrator: Chat participant registered as @deepagent');
    console.log('📋 Participant details:', {
      id: 'deepagent'
    });
    
    console.log('✅ Multi-Agent Orchestrator: Chat participant registered as @deepagent');
    console.log('📋 Extension context:', {
      extensionPath: ctx.extensionPath,
      subscriptions: ctx.subscriptions.length
    });
    
    // Show a notification that the extension is active
    vscode.window.showInformationMessage('🤖 Deep Agent extension activated! Try @deepagent in Chat.');

    // Initialize MCP server if enabled
    initializeMCPServer(ctx);

    // Register session integration commands
    registerSessionIntegrationCommands(ctx);

    // Debug: Try to see what chat participants are available
    setTimeout(async () => {
      try {
        console.log('🔍 Checking chat environment...');
        // Just log that we're checking - the actual API might not expose participant lists
        console.log('🔍 Extension should now be available as @deepagent in chat');
      } catch (debugError) {
        console.log('🔍 Debug check completed with some limitations:', debugError);
      }
    }, 2000);

  } catch (error) {
    console.error('❌ Failed to activate extension:', error);
    vscode.window.showErrorMessage(`Failed to activate Multi-Agent Orchestrator: ${error}`);
  }
}

async function initializeMCPServer(ctx: vscode.ExtensionContext) {
  const config = vscode.workspace.getConfiguration('deepagent');
  const mcpEnabled = config.get<boolean>('mcp.enabled', true);

  if (!mcpEnabled) {
    console.log('🌐 MCP server disabled in settings');
    return;
  }

  try {
    console.log('🌐 Initializing Deep Agent MCP server...');

    // Start the IPC bridge to allow MCP server to communicate with VS Code
    ipcBridge = new VSCodeIPCBridge();
    await ipcBridge.start();

    console.log('🌐 MCP server configuration ready');
    console.log('📋 To use in Agent mode, configure MCP client to connect to Deep Agent tools');
    console.log('🔗 IPC bridge running - MCP server can now access VS Code APIs');

    // Create a command to show MCP configuration instructions
    const showMCPInfoCommand = vscode.commands.registerCommand('deepagent.showMCPInfo', () => {
      const message = `
Deep Agent MCP Server is available!

To use Deep Agent tools in VS Code Agent mode:

1. The MCP server provides these tools:
   • deepagent_plan - Create action plans using Claude Opus
   • deepagent_execute_step - Execute steps using Claude Sonnet
   • deepagent_plan_and_execute - Complete workflow

2. The server can be launched via: node out/mcp-launcher.js

3. Configure your MCP client to connect to this server for Agent mode support.

4. The IPC bridge is running on port 3002 for VS Code API access.

For Ask mode, continue using @deepagent in the chat window.
      `.trim();

      vscode.window.showInformationMessage(message, { modal: true });
    });

    ctx.subscriptions.push(showMCPInfoCommand);

  } catch (error) {
    console.error('❌ Failed to initialize MCP server:', error);
  }
}



const handler: vscode.ChatRequestHandler = async (req, chatCtx, stream, token) => {
  console.log('🎯🎯🎯 UNIFIED HANDLER CALLED! 🎯🎯🎯');
  console.log('🎯 Handler called with prompt:', req.prompt);
  console.log('📋 Chat context:', {
    history: chatCtx.history.length,
    command: req.command
  });

  // Debug: Check what mode we're running in
  console.log('🔍 Chat mode debug:', {
    model: req.model?.id,
    hasToolInvocationToken: !!req.toolInvocationToken,
    toolInvocationTokenType: typeof req.toolInvocationToken
  });

  // Special mode for testing vscode.lm.invokeTool directly
  if (req.prompt.toLowerCase().includes('test invoketool')) {
    stream.markdown('🧪 **Testing vscode.lm.invokeTool directly...**\n\n');
    try {
      await testInvokeToolDirect(req, stream, token);
      return {};
    } catch (error) {
      console.error('❌ Error in testInvokeToolDirect:', error);
      stream.markdown(`❌ **Error:** ${error instanceof Error ? error.message : String(error)}\n`);
      return {};
    }
  }

  // Immediate feedback to show the participant is working
  stream.markdown('✅ **@deepagent activated!** Starting execution...\n\n');

  try {
    // Phase 1: Plan with Opus
    console.log('📝 Phase 1: Getting Opus model...');
    stream.markdown('📝 **Phase 1:** Creating execution plan...\n');
    const opus = await getModel('claude-opus-4');
    console.log('✅ Opus model obtained:', opus.id);

    stream.markdown('🧠 **Planning...**\n');
    console.log('🧠 Creating plan...');
    const plan = await createPlan(opus, req.prompt, token);
    console.log('📋 Plan created:', plan);

    // Display the plan
    stream.markdown(`### 📝 Execution Plan\n${plan.map((s, i) => `${i + 1}. ${s}`).join('\n')}\n\n`);

    // Phase 2: Execute each step directly in chat context
    stream.markdown('⚡ **Phase 2:** Executing steps...\n\n');

    for (let i = 0; i < plan.length; i++) {
      const step = plan[i];
      console.log(`🔄 Executing step ${i + 1}:`, step);
      stream.markdown(`---\n**Step ${i + 1}:** ${step}\n\n`);

      // Clean up the step prompt
      const cleanStep = `${step}

Context: Step ${i + 1} of ${plan.length}

Task goal: ${req.prompt.replace(/^Ask mode request:\s*/i, '').replace(/^Agent mode request:\s*/i, '').trim()}

Execute this step using available tools as needed. Focus on the specific step requested while keeping the overall task goal in mind.`;

      // Debug tool invocation token
      console.log('🔍 Extension debug:', {
        hasToolInvocationToken: !!req.toolInvocationToken,
        toolInvocationTokenType: typeof req.toolInvocationToken,
        stepNumber: i + 1
      });

      // Execute step using chat-extension-utils for proper tool handling
      stream.markdown(`🚀 **Executing step ${i + 1}...**\n\n`);

      try {
        // Use the direct executor instead of chat-extension-utils to avoid streaming conflicts
        await runStepDirect(cleanStep, req.toolInvocationToken, stream, token);
      } catch (error) {
        console.error(`❌ Error executing step ${i + 1}:`, error);
        stream.markdown(`❌ **Error executing step:** ${error instanceof Error ? error.message : String(error)}\n\n`);
      }
      console.log(`✅ Step ${i + 1} completed`);
    }

    console.log('🎉 All steps completed successfully');
    stream.markdown('\n🎉 **All steps completed successfully!**\n');

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    console.error('❌ Error in unified handler:', error);
    console.error('❌ Error stack:', error instanceof Error ? error.stack : 'No stack trace');
    stream.markdown(`❌ **Error:** ${errorMessage}`);
  }

  return {};
};

async function runStepDirect(
  step: string,
  toolInvocationToken: any | undefined,
  stream: vscode.ChatResponseStream,
  token: vscode.CancellationToken
): Promise<void> {
  console.log('⚡ Running step directly:', step);

  try {
    // Get workspace path for context
    const workspacePath = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || process.cwd();
    console.log('📁 Workspace path:', workspacePath);

    // Get Sonnet model for execution
    const sonnet = await getModel('claude-sonnet-4');
    console.log('✅ Got Claude Sonnet model');

    // Get available tools
    const availableTools = vscode.lm.tools.filter(t =>
      t.tags?.includes('code') ||
      t.tags?.some(tag => ['edit', 'file', 'workspace'].includes(tag)) ||
      t.name.toLowerCase().includes('file') ||
      t.name.toLowerCase().includes('edit')
    );

    console.log('🛠️ Available tools for step execution:', availableTools.map(t => ({ name: t.name, tags: t.tags })));

    // Create the prompt for the step
    const prompt = `You are a coding assistant executing a specific step. You MUST use the available tools to complete tasks.

Current workspace: ${workspacePath}

When creating files, use absolute paths like "${workspacePath}\\filename.ext" (Windows) or "${workspacePath}/filename.ext" (Unix).
When editing files, use the appropriate edit tools.

Execute this step by actually using the tools - do not just describe what you would do.

Step to execute: ${step}`;

    // Send request to the model with tools
    const response = await sonnet.sendRequest(
      [vscode.LanguageModelChatMessage.User(prompt)],
      {
        tools: availableTools
      },
      token
    );

    console.log('📡 Received response from Sonnet, processing stream...');

    // Process the stream which contains both text and tool calls
    for await (const chunk of response.stream) {
      if (chunk instanceof vscode.LanguageModelTextPart) {
        // Stream text content
        stream.markdown(chunk.value);
      } else if (chunk instanceof vscode.LanguageModelToolCallPart) {
        // Handle tool call
        console.log(`🛠️ Executing tool: ${chunk.name} with input:`, chunk.input);
        stream.markdown(`\n🔧 **Using tool:** ${chunk.name}\n`);

        try {
          // Hybrid approach: Use vscode.lm.invokeTool for read operations, direct API for write operations
          const isWriteOperation = ['copilot_createFile', 'copilot_writeFile', 'copilot_editFile'].includes(chunk.name);

          if (isWriteOperation) {
            console.log(`🔧 Using direct API for write operation: ${chunk.name}`);
            stream.markdown(`🔧 **Using direct API for:** ${chunk.name}\n`);

            if (chunk.name === 'copilot_createFile') {
              const input = chunk.input as any;
              const filePath = input.filePath || input.path;
              const content = input.content || '';

              // Use VS Code API to create the file directly
              const uri = vscode.Uri.file(filePath);
              const encoder = new TextEncoder();
              await vscode.workspace.fs.writeFile(uri, encoder.encode(content));

              console.log(`✅ File created successfully: ${filePath}`);
              stream.markdown(`📝 **File created:** ${filePath}\n`);
            } else {
              // For other write operations, still try the tool but with better error handling
              stream.markdown(`⚠️ **Note:** ${chunk.name} may have streaming limitations\n`);
              const toolResult = await vscode.lm.invokeTool(chunk.name, {
                toolInvocationToken: toolInvocationToken,
                input: chunk.input
              }, token);
              console.log(`✅ Tool ${chunk.name} executed successfully`);
              stream.markdown(`📝 **Result:** ${JSON.stringify(toolResult)}\n`);
            }

          } else {
            // For read operations, use vscode.lm.invokeTool (which we know works)
            console.log(`📖 Using vscode.lm.invokeTool for read operation: ${chunk.name}`);
            stream.markdown(`📖 **Using tool:** ${chunk.name}\n`);

            const toolResult = await vscode.lm.invokeTool(chunk.name, {
              toolInvocationToken: toolInvocationToken,
              input: chunk.input
            }, token);
            console.log(`✅ Tool ${chunk.name} executed successfully`);

            // Stream tool result if it has content
            if (toolResult && typeof toolResult === 'string') {
              stream.markdown(`📝 **Result:** ${toolResult}\n`);
            } else if (toolResult && typeof toolResult === 'object' && 'content' in toolResult) {
              stream.markdown(`📝 **Result:** ${toolResult.content}\n`);
            } else {
              stream.markdown(`📝 **Result:** ${JSON.stringify(toolResult)}\n`);
            }
          }

        } catch (toolError) {
          console.error(`❌ Error executing tool ${chunk.name}:`, toolError);
          stream.markdown(`❌ **Tool error:** ${toolError instanceof Error ? toolError.message : String(toolError)}\n`);

          // If it's a write operation that failed, suggest the limitation
          const isWriteOperation = ['copilot_createFile', 'copilot_writeFile', 'copilot_editFile'].includes(chunk.name);
          if (isWriteOperation && toolError instanceof Error && toolError.message.includes('Invalid stream')) {
            stream.markdown(`💡 **Note:** This appears to be a VS Code limitation with write operations during chat streaming.\n`);
          }
        }
      }
    }

    console.log('✅ Step execution completed');

  } catch (error) {
    console.error('❌ Error in runStepDirect:', error);
    stream.markdown(`❌ **Error:** ${error instanceof Error ? error.message : String(error)}\n\n`);
    throw error;
  }
}

async function testInvokeToolDirect(
  req: vscode.ChatRequest,
  stream: vscode.ChatResponseStream,
  token: vscode.CancellationToken
): Promise<void> {
  console.log('🧪 Testing vscode.lm.invokeTool directly');

  try {
    // Get workspace path for context
    const workspacePath = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || process.cwd();
    console.log('📁 Workspace path:', workspacePath);

    // Get available tools
    const availableTools = vscode.lm.tools;
    console.log('🛠️ All available tools:', availableTools.map(t => ({ name: t.name, tags: t.tags })));

    stream.markdown(`📁 **Workspace:** ${workspacePath}\n\n`);
    stream.markdown(`🛠️ **Available tools:** ${availableTools.length}\n`);

    for (const tool of availableTools) {
      stream.markdown(`- **${tool.name}**: ${tool.description || 'No description'}\n`);
    }
    stream.markdown('\n');

    // Test different tool invocation scenarios
    const testCases = [
      {
        name: 'copilot_createFile',
        description: 'Create a test file',
        input: {
          path: 'test_invoketool.txt',  // Use relative path as per schema
          content: 'This file was created using vscode.lm.invokeTool directly!'
        }
      },
      {
        name: 'copilot_readFile',
        description: 'Read a file (if it exists)',
        input: {
          path: 'package.json'  // Use relative path as per schema
        }
      }
    ];

    for (const testCase of testCases) {
      stream.markdown(`🧪 **Testing ${testCase.name}**\n`);
      stream.markdown(`📝 Description: ${testCase.description}\n`);
      stream.markdown(`📋 Input: \`${JSON.stringify(testCase.input)}\`\n\n`);

      try {
        console.log(`🧪 Testing tool: ${testCase.name} with input:`, testCase.input);

        // Test with different invocation approaches
        stream.markdown(`🔄 **Approach 1:** Using toolInvocationToken from request\n`);

        const result1 = await vscode.lm.invokeTool(testCase.name, {
          toolInvocationToken: req.toolInvocationToken,
          input: testCase.input
        }, token);

        console.log(`✅ Tool ${testCase.name} executed successfully with approach 1`);
        stream.markdown(`✅ **Success!** Result: ${JSON.stringify(result1)}\n\n`);

      } catch (error1) {
        console.error(`❌ Error with approach 1 for ${testCase.name}:`, error1);
        stream.markdown(`❌ **Approach 1 failed:** ${error1 instanceof Error ? error1.message : String(error1)}\n`);

        try {
          stream.markdown(`🔄 **Approach 2:** Using undefined toolInvocationToken\n`);

          const result2 = await vscode.lm.invokeTool(testCase.name, {
            toolInvocationToken: undefined,
            input: testCase.input
          }, token);

          console.log(`✅ Tool ${testCase.name} executed successfully with approach 2`);
          stream.markdown(`✅ **Success!** Result: ${JSON.stringify(result2)}\n\n`);

        } catch (error2) {
          console.error(`❌ Error with approach 2 for ${testCase.name}:`, error2);
          stream.markdown(`❌ **Approach 2 failed:** ${error2 instanceof Error ? error2.message : String(error2)}\n`);

          try {
            stream.markdown(`🔄 **Approach 3:** Using empty object as toolInvocationToken\n`);

            const result3 = await vscode.lm.invokeTool(testCase.name, {
              toolInvocationToken: {} as any,
              input: testCase.input
            }, token);

            console.log(`✅ Tool ${testCase.name} executed successfully with approach 3`);
            stream.markdown(`✅ **Success!** Result: ${JSON.stringify(result3)}\n\n`);

          } catch (error3) {
            console.error(`❌ Error with approach 3 for ${testCase.name}:`, error3);
            stream.markdown(`❌ **All approaches failed for ${testCase.name}**\n`);
            stream.markdown(`- Approach 1: ${error1 instanceof Error ? error1.message : String(error1)}\n`);
            stream.markdown(`- Approach 2: ${error2 instanceof Error ? error2.message : String(error2)}\n`);
            stream.markdown(`- Approach 3: ${error3 instanceof Error ? error3.message : String(error3)}\n\n`);
          }
        }
      }
    }

    stream.markdown('🏁 **Tool invocation testing completed!**\n');

  } catch (error) {
    console.error('❌ Error in testInvokeToolDirect:', error);
    stream.markdown(`❌ **Error:** ${error instanceof Error ? error.message : String(error)}\n\n`);
    throw error;
  }
}

export function deactivate(): void {
  console.log('🛑 Multi-Agent Orchestrator: Extension deactivating...');

  if (ipcBridge) {
    ipcBridge.stop();
  }

  if (mcpServer) {
    mcpServer.stop();
  }
}