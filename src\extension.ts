import * as vscode from 'vscode';
import { DeepAgentMCPServer } from './mcp-server';
import { VSCodeIPCBridge } from './ipc-bridge';
import { registerSessionIntegrationCommands } from './session-integration-example';
import { DeepAgentTool } from './deep-agent-tool';

let mcpServer: DeepAgentMCPServer | undefined;
let ipcBridge: VSCodeIPCBridge | undefined;

export function activate(ctx: vscode.ExtensionContext): void {
  console.log('🚀 Multi-Agent Orchestrator: Extension activating...');

  try {
    // Register a test command first
    const testCommand = vscode.commands.registerCommand('deepagent.test', () => {
      vscode.window.showInformationMessage('Deep Agent extension is active!');
      console.log('🧪 Test command executed - extension is active');
    });
    ctx.subscriptions.push(testCommand);

    // Register the Language Model Tool instead of chat participant
    console.log('🔧 Registering Deep Agent as Language Model Tool...');

    try {
      const deepAgentTool = new DeepAgentTool();
      const toolRegistration = vscode.lm.registerTool('deepagent', deepAgentTool);
      ctx.subscriptions.push(toolRegistration);

      console.log('✅ Language Model Tool registered successfully');
      console.log('📋 Tool name: deepagent');

    } catch (toolError) {
      console.error('❌ Failed to register Language Model Tool:', toolError);
      throw toolError;
    }

    console.log('✅ Multi-Agent Orchestrator: Language Model Tool registered as "deepagent"');
    console.log('📋 Tool details:', {
      name: 'deepagent',
      type: 'LanguageModelTool'
    });

    console.log('✅ Extension context:', {
      extensionPath: ctx.extensionPath,
      subscriptions: ctx.subscriptions.length
    });

    // Show a notification that the extension is active
    vscode.window.showInformationMessage('🤖 Deep Agent tool activated! Use in Agent/Edit mode or reference with #deepagent.');

    // Initialize MCP server if enabled
    initializeMCPServer(ctx);

    // Register session integration commands
    registerSessionIntegrationCommands(ctx);

    // Debug: Try to see what chat participants are available
    setTimeout(async () => {
      try {
        console.log('🔍 Checking chat environment...');
        // Just log that we're checking - the actual API might not expose participant lists
        console.log('🔍 Extension should now be available as @deepagent in chat');
      } catch (debugError) {
        console.log('🔍 Debug check completed with some limitations:', debugError);
      }
    }, 2000);

  } catch (error) {
    console.error('❌ Failed to activate extension:', error);
    vscode.window.showErrorMessage(`Failed to activate Multi-Agent Orchestrator: ${error}`);
  }
}

async function initializeMCPServer(ctx: vscode.ExtensionContext) {
  const config = vscode.workspace.getConfiguration('deepagent');
  const mcpEnabled = config.get<boolean>('mcp.enabled', true);

  if (!mcpEnabled) {
    console.log('🌐 MCP server disabled in settings');
    return;
  }

  try {
    console.log('🌐 Initializing Deep Agent MCP server...');

    // Start the IPC bridge to allow MCP server to communicate with VS Code
    ipcBridge = new VSCodeIPCBridge();
    await ipcBridge.start();

    console.log('🌐 MCP server configuration ready');
    console.log('📋 To use in Agent mode, configure MCP client to connect to Deep Agent tools');
    console.log('🔗 IPC bridge running - MCP server can now access VS Code APIs');

    // Create a command to show MCP configuration instructions
    const showMCPInfoCommand = vscode.commands.registerCommand('deepagent.showMCPInfo', () => {
      const message = `
Deep Agent MCP Server is available!

To use Deep Agent tools in VS Code Agent mode:

1. The MCP server provides these tools:
   • deepagent_plan - Create action plans using Claude Opus
   • deepagent_execute_step - Execute steps using Claude Sonnet
   • deepagent_plan_and_execute - Complete workflow

2. The server can be launched via: node out/mcp-launcher.js

3. Configure your MCP client to connect to this server for Agent mode support.

4. The IPC bridge is running on port 3002 for VS Code API access.

For Ask mode, continue using @deepagent in the chat window.
      `.trim();

      vscode.window.showInformationMessage(message, { modal: true });
    });

    ctx.subscriptions.push(showMCPInfoCommand);

  } catch (error) {
    console.error('❌ Failed to initialize MCP server:', error);
  }
}



// Chat participant handler removed - now using Language Model Tool approach

// Unused test function removed - functionality now in Language Model Tool

export function deactivate(): void {
  console.log('🛑 Multi-Agent Orchestrator: Extension deactivating...');

  if (ipcBridge) {
    ipcBridge.stop();
  }

  if (mcpServer) {
    mcpServer.stop();
  }
}