import * as vscode from 'vscode';
import { DeepAgentTool } from './deep-agent-tool';

// MCP server and IPC bridge imports removed - using Language Model Tool only
// import { DeepAgentMCPServer } from './mcp-server';
// import { VSCodeIPCBridge } from './ipc-bridge';
// import { registerSessionIntegrationCommands } from './session-integration-example';

// let mcpServer: DeepAgentMCPServer | undefined;
// let ipcBridge: VSCodeIPCBridge | undefined;

export function activate(ctx: vscode.ExtensionContext): void {
  console.log('🚀 Multi-Agent Orchestrator: Extension activating...');

  try {
    // Register a test command first
    const testCommand = vscode.commands.registerCommand('deepagent.test', () => {
      vscode.window.showInformationMessage('Deep Agent extension is active!');
      console.log('🧪 Test command executed - extension is active');
    });
    ctx.subscriptions.push(testCommand);

    // Register the Language Model Tool instead of chat participant
    console.log('🔧 Registering Deep Agent as Language Model Tool...');

    try {
      const deepAgentTool = new DeepAgentTool();
      const toolRegistration = vscode.lm.registerTool('deepagent_lmt', deepAgentTool);
      ctx.subscriptions.push(toolRegistration);

      console.log('✅ Language Model Tool registered successfully');
      console.log('📋 Tool name: deepagent_lmt');

    } catch (toolError) {
      console.error('❌ Failed to register Language Model Tool:', toolError);
      throw toolError;
    }

    console.log('✅ Multi-Agent Orchestrator: Language Model Tool registered as "deepagent_lmt"');
    console.log('📋 Tool details:', {
      name: 'deepagent_lmt',
      type: 'LanguageModelTool'
    });

    console.log('✅ Extension context:', {
      extensionPath: ctx.extensionPath,
      subscriptions: ctx.subscriptions.length
    });

    // Show a notification that the extension is active
    vscode.window.showInformationMessage('🤖 Deep Agent tool activated! Use in Agent/Edit mode or reference with #deepagent_lmt.');

    // MCP server and IPC bridge disabled - using Language Model Tool only
    console.log('🚫 MCP server and IPC bridge disabled - using Language Model Tool interface only');

    // Session integration commands disabled for Language Model Tool approach
    // registerSessionIntegrationCommands(ctx);

    // Debug: Try to see what chat participants are available
    setTimeout(async () => {
      try {
        console.log('🔍 Checking chat environment...');
        // Just log that we're checking - the actual API might not expose participant lists
        console.log('🔍 Extension should now be available as #deepagent_lmt in Agent/Edit mode');
      } catch (debugError) {
        console.log('🔍 Debug check completed with some limitations:', debugError);
      }
    }, 2000);

  } catch (error) {
    console.error('❌ Failed to activate extension:', error);
    vscode.window.showErrorMessage(`Failed to activate Multi-Agent Orchestrator: ${error}`);
  }
}

// MCP server initialization removed - using Language Model Tool only



// Chat participant handler removed - now using Language Model Tool approach

// Unused test function removed - functionality now in Language Model Tool

export function deactivate(): void {
  console.log('🛑 Deep Agent: Extension deactivating...');
  // MCP server and IPC bridge cleanup removed - using Language Model Tool only
}