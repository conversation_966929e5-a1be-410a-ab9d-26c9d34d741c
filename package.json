{"name": "multi-agent-orchestrator", "displayName": "Multi-Agent Orchestrator", "description": "VS Code extension with Language Model Tool that plans with Claude Opus and executes with Claude <PERSON> for complex development tasks.", "version": "0.0.1", "publisher": "deepagent", "engines": {"vscode": "^1.95.0"}, "categories": ["AI", "Machine Learning"], "activationEvents": ["onLanguageModelTool:deepagent_lmt"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "deepagent.testTool", "title": "Test Deep Agent Language Model Tool"}], "languageModelTools": [{"name": "deepagent_lmt", "displayName": "Deep Agent Language Model Tool", "description": "Multi-agent orchestrator that plans with <PERSON> and executes with <PERSON>. Can create files, edit code, and perform complex development tasks.", "modelDescription": "A multi-agent orchestrator that uses <PERSON> for planning and <PERSON> for execution. Capable of creating files, editing code, and performing complex development tasks through VS Code tools.", "inputSchema": {"type": "object", "properties": {"task": {"type": "string", "description": "The task or request to be planned and executed by the multi-agent system"}}, "required": ["task"]}}]}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "enabledApiProposals": [], "devDependencies": {"@types/node": "^18.x", "@types/vscode": "^1.95.0", "typescript": "^5.3.0"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.13.1", "@vscode/chat-extension-utils": "latest"}}