{"name": "multi-agent-orchestrator", "displayName": "Multi-Agent Orchestrator", "description": "VS Code Chat extension with @deepagent participant that plans with Opus and executes with Sonnet. Also provides MCP server for Agent mode.", "version": "0.0.1", "engines": {"vscode": "^1.85.0"}, "categories": ["AI", "Chat Participants"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"languageModelTools": [{"name": "deepagent", "displayName": "Deep Agent Orchestrator", "description": "Multi-agent orchestrator that plans with <PERSON> and executes with <PERSON>. Can create files, edit code, and perform complex development tasks.", "inputSchema": {"type": "object", "properties": {"task": {"type": "string", "description": "The task or request to be planned and executed by the multi-agent system"}}, "required": ["task"]}}], "commands": [{"command": "deepagent.test", "title": "Test Deep Agent"}], "configuration": {"title": "Deep Agent", "properties": {"deepagent.mcp.enabled": {"type": "boolean", "default": true, "description": "Enable MCP server for Agent mode support"}, "deepagent.mcp.port": {"type": "number", "default": 3001, "description": "Port for the MCP server"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "enabledApiProposals": [], "devDependencies": {"@types/node": "^18.x", "@types/vscode": "^1.85.0", "typescript": "^5.3.0"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.13.1", "@vscode/chat-extension-utils": "latest"}}