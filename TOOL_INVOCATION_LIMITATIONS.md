# VS Code Tool Invocation Limitations

## 🚨 **Critical Issue: "Invalid stream" Error**

### **Problem Description**
When using `vscode.lm.invokeTool` for write operations (like `copilot_createFile`) in certain contexts, VS Code throws an "Invalid stream" error.

### **Root Cause**
The error occurs because:
1. **Write tools require a valid `toolInvocationToken`** tied to a specific chat session
2. **MCP servers run in separate process contexts** where the original chat session token is not available
3. **VS Code's tool invocation system expects a streaming context** that doesn't exist in MCP environments

### **Affected Tools**
❌ **Write Operations (Always Fail in MCP):**
- `copilot_createFile`
- `copilot_writeFile` 
- `copilot_editFile`
- `copilot_insertEdit`

✅ **Read Operations (Work Fine):**
- `copilot_readFile`
- `copilot_findFiles`
- `copilot_findTextInFiles`
- `copilot_semanticSearch`

### **Context-Specific Behavior**

| Context | Read Tools | Write Tools | Notes |
|---------|------------|-------------|-------|
| **Chat Participant** | ✅ Works | ❌ Fails | Even in Agent/Edit mode |
| **MCP Server** | ✅ Works | ❌ Fails | No valid stream context |
| **Direct VS Code API** | N/A | ✅ Works | Bypass tool system entirely |

### **Solutions Implemented**

#### **1. Hybrid Approach (Current)**
```typescript
const isWriteOperation = ['copilot_createFile', 'copilot_writeFile', 'copilot_editFile'].includes(toolName);

if (isWriteOperation) {
  // Use direct VS Code API
  if (toolName === 'copilot_createFile') {
    const uri = vscode.Uri.file(filePath);
    await vscode.workspace.fs.writeFile(uri, encoder.encode(content));
  }
} else {
  // Use vscode.lm.invokeTool for read operations
  const result = await vscode.lm.invokeTool(toolName, {
    toolInvocationToken: token,
    input: input
  });
}
```

#### **2. Error Handling & User Guidance**
- Detect "Invalid stream" errors
- Provide clear explanations of the limitation
- Suggest using chat participant mode for write operations

### **Workarounds**

#### **For Users:**
1. **Use Chat Participant Mode** (`@deepagent`) for file operations
2. **Use MCP Server Mode** only for read-only analysis and planning
3. **Combine both modes**: Plan with MCP, execute with chat participant

#### **For Developers:**
1. **Implement hybrid tool handling** (read tools vs write tools)
2. **Use direct VS Code APIs** for write operations
3. **Provide clear error messages** explaining the limitation

### **Future Considerations**
- This appears to be a fundamental limitation of VS Code's tool invocation architecture
- Microsoft may address this in future VS Code releases
- Monitor VS Code API updates for changes to tool invocation requirements

### **Final Solution: Enhanced Hybrid Architecture**

After extensive research and testing, the optimal approach is:

#### **Architecture Decision:**
- **Keep both Chat Participant AND MCP Server**
- **Use different execution strategies** based on context
- **Leverage the strengths of each approach**

#### **Implementation:**
1. **Chat Participant Mode** (`@deepagent`):
   - Has access to `toolInvocationToken`
   - Can use read tools successfully
   - Uses direct VS Code API for write operations
   - Best for interactive development

2. **MCP Server Mode** (Agent mode):
   - No `toolInvocationToken` available
   - Uses MCP-compatible execution with direct VS Code APIs
   - Provides detailed analysis and instructions
   - Best for automated workflows and external tool integration

#### **Why Pure MCP Won't Work:**
- MCP servers are **standalone processes** by design
- `toolInvocationToken` is **VS Code-specific** and cannot cross process boundaries
- This is a **fundamental architectural limitation**, not a bug
- MCP is meant to be **client-agnostic**

### **References**
- [GitHub Discussion #156285](https://github.com/orgs/community/discussions/156285) - Same "Invalid stream" error
- [GitHub Discussion #152281](https://github.com/orgs/community/discussions/152281) - Working example with `toolInvocationToken: undefined`
- [VS Code Chat Extension API](https://code.visualstudio.com/api/extension-guides/chat) - Official documentation

### **Testing Results**
- ✅ **o3's schema fixes**: `path` vs `filePath`, relative vs absolute paths
- ❌ **o3's mode theory**: Ask vs Agent mode doesn't solve the issue for chat participants
- ✅ **Hybrid approach**: Direct VS Code API works for write operations
- ✅ **Read tools**: Work fine with `toolInvocationToken` from chat context
- ✅ **MCP-compatible execution**: Works without tool invocation tokens
