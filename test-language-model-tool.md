# Testing Deep Agent Language Model Tool

## Test Instructions

1. **Open VS Code Extension Development Host**
   - Run `F5` or use "Run Extension" from the Run and Debug panel
   - This will open a new VS Code window with the Deep Agent extension loaded

2. **Test the Language Model Tool in Agent Mode**
   - Open the Chat panel (Ctrl+Shift+I or View > Chat)
   - Switch to **Agent mode** (not Ask mode)
   - Use the Deep Agent tool by typing: `#deepagent_lmt`
   - Provide a task like: `Create a simple hello.txt file with "Hello World" content`

3. **Expected Behavior**
   - The tool should be available in Agent mode
   - It should receive the `toolInvocationToken` from the Agent session
   - It should successfully invoke `copilot_createFile` without "Invalid stream" errors
   - The file should be created successfully

4. **Test Commands**
   ```
   #deepagent Create a simple hello.txt file with "Hello World" content
   ```

   ```
   #deepagent Create a TypeScript function that validates email addresses and save it to utils/email-validator.ts
   ```

## Key Success Indicators

✅ **Tool Registration**: Extension logs show "Language Model Tool registered successfully"
✅ **Tool Availability**: <PERSON><PERSON> appears in Agent mode when typing `#deepagent`
✅ **Token Access**: <PERSON>l receives `toolInvocationToken` (check console logs)
✅ **Tool Invocation**: Successfully calls `copilot_createFile` without "Invalid stream" error
✅ **File Creation**: Files are actually created in the workspace

## Debugging

Check the VS Code Developer Console (Help > Toggle Developer Tools) for logs:
- Look for "🔧 Deep Agent Tool: invoke called"
- Check "🔑 Has toolInvocationToken: true"
- Verify "✅ Tool copilot_createFile executed successfully"

## Comparison with Previous Approach

**Chat Participant (Previous)**:
- ❌ "Invalid stream" error when invoking tools
- ❌ No access to proper `toolInvocationToken`
- ❌ Required workarounds and hybrid approaches

**Language Model Tool (New)**:
- ✅ Runs in same process as Agent/Edit sessions
- ✅ Has access to `toolInvocationToken` via `LanguageModelToolInvocationOptions`
- ✅ Can successfully invoke other tools like `copilot_createFile`
- ✅ No "Invalid stream" errors expected
