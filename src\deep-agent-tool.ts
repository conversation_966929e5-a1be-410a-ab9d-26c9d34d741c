import * as vscode from 'vscode';
import { getModel } from './models';
import { createPlan } from './planner';

/**
 * Input schema for the Deep Agent Language Model Tool
 */
interface DeepAgentToolInput {
  task: string;
}

/**
 * Deep Agent Language Model Tool
 * 
 * This tool implements the VS Code Language Model Tool interface to provide
 * multi-agent orchestration capabilities that can be invoked by Agent/Edit mode.
 * 
 * Key advantages over chat participants:
 * - Runs in the same process as Agent/Edit chat sessions
 * - Has access to toolInvocationToken for proper tool invocation
 * - Can successfully invoke other tools like copilot_createFile
 */
export class DeepAgentTool implements vscode.LanguageModelTool<DeepAgentToolInput> {

  // Add tags property for tool discovery
  public readonly tags: string[] = ['development', 'coding', 'agent', 'orchestrator'];

  // Add displayName for better UI representation
  public readonly displayName: string = 'Deep Agent Language Model Tool';
  
  async prepareInvocation(
    options: vscode.LanguageModelToolInvocationPrepareOptions<DeepAgentToolInput>,
    token: vscode.CancellationToken
  ): Promise<vscode.PreparedToolInvocation> {
    
    console.log('🔧 Deep Agent Tool: prepareInvocation called');
    console.log('📋 Input:', options.input);
    
    // Return the prepared invocation - this is where we can validate input
    // and prepare any resources needed for the actual invocation
    return {
      invocationMessage: `Planning and executing: ${options.input.task}`
    };
  }

  async invoke(
    options: vscode.LanguageModelToolInvocationOptions<DeepAgentToolInput>,
    token: vscode.CancellationToken
  ): Promise<vscode.LanguageModelToolResult> {
    
    console.log('🚀 Deep Agent Tool: invoke called');
    console.log('📋 Input:', options.input);
    console.log('🔑 Has toolInvocationToken:', !!options.toolInvocationToken);
    console.log('🔑 Token type:', typeof options.toolInvocationToken);
    
    try {
      // Phase 1: Plan with Opus
      console.log('📝 Phase 1: Creating execution plan...');
      const opus = await getModel('claude-opus-4');
      console.log('✅ Opus model obtained:', opus.id);

      const plan = await createPlan(opus, options.input.task, token);
      console.log('📋 Plan created:', plan);

      // Build result content
      let resultContent = `# Deep Agent Execution Plan\n\n`;
      resultContent += `**Task:** ${options.input.task}\n\n`;
      resultContent += `## 📝 Execution Plan\n`;
      plan.forEach((step, i) => {
        resultContent += `${i + 1}. ${step}\n`;
      });
      resultContent += `\n## ⚡ Execution Results\n\n`;

      // Phase 2: Execute each step using the toolInvocationToken
      for (let i = 0; i < plan.length; i++) {
        const step = plan[i];
        console.log(`🔄 Executing step ${i + 1}:`, step);
        
        resultContent += `### Step ${i + 1}: ${step}\n\n`;

        try {
          const stepResult = await this.executeStep(
            step, 
            options.input.task,
            options.toolInvocationToken,
            token
          );
          
          resultContent += `✅ **Result:** ${stepResult}\n\n`;
          console.log(`✅ Step ${i + 1} completed successfully`);
          
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          console.error(`❌ Error executing step ${i + 1}:`, error);
          resultContent += `❌ **Error:** ${errorMessage}\n\n`;
        }
      }

      resultContent += `\n🎉 **Execution completed!**\n`;
      console.log('🎉 All steps completed');

      return {
        content: [new vscode.LanguageModelTextPart(resultContent)]
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('❌ Error in Deep Agent Tool invoke:', error);
      
      return {
        content: [new vscode.LanguageModelTextPart(
          `❌ **Deep Agent Error:** ${errorMessage}\n\n` +
          `Please try again or contact support if the issue persists.`
        )]
      };
    }
  }

  /**
   * Execute a single step using Claude Sonnet and available tools
   */
  private async executeStep(
    step: string,
    originalTask: string,
    toolInvocationToken: any,
    token: vscode.CancellationToken
  ): Promise<string> {
    
    console.log('⚡ Executing step:', step);
    console.log('🔑 Using toolInvocationToken:', !!toolInvocationToken);

    // Get workspace path for context
    const workspacePath = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || process.cwd();
    console.log('📁 Workspace path:', workspacePath);

    // Get Sonnet model for execution
    const sonnet = await getModel('claude-sonnet-4');
    console.log('✅ Got Claude Sonnet model');

    // Get available tools
    const availableTools = vscode.lm.tools.filter(t =>
      t.tags?.includes('code') ||
      t.tags?.some(tag => ['edit', 'file', 'workspace'].includes(tag)) ||
      t.name.toLowerCase().includes('file') ||
      t.name.toLowerCase().includes('edit')
    );

    console.log('🛠️ Available tools for step execution:', availableTools.map(t => ({ name: t.name, tags: t.tags })));

    // Create the prompt for the step
    const prompt = `You are a coding assistant executing a specific step. You MUST use the available tools to complete tasks.

Current workspace: ${workspacePath}

When creating files, use workspace-relative paths like "filename.ext" or "folder/filename.ext".
When editing files, use the appropriate edit tools.

Execute this step by actually using the tools - do not just describe what you would do.

Original task: ${originalTask}
Current step: ${step}

Execute this step now using the available tools.`;

    // Send request to the model with tools
    const response = await sonnet.sendRequest(
      [vscode.LanguageModelChatMessage.User(prompt)],
      {
        tools: availableTools
      },
      token
    );

    console.log('📡 Received response from Sonnet, processing stream...');

    let stepResult = '';
    let toolsUsed: string[] = [];

    // Process the stream which contains both text and tool calls
    for await (const chunk of response.stream) {
      if (chunk instanceof vscode.LanguageModelTextPart) {
        // Collect text content
        stepResult += chunk.value;
      } else if (chunk instanceof vscode.LanguageModelToolCallPart) {
        // Handle tool call using the toolInvocationToken
        console.log(`🛠️ Executing tool: ${chunk.name} with input:`, chunk.input);
        toolsUsed.push(chunk.name);

        try {
          // THIS IS THE KEY: Use the toolInvocationToken from the Language Model Tool context
          const toolResult = await vscode.lm.invokeTool(chunk.name, {
            toolInvocationToken: toolInvocationToken, // This should work now!
            input: chunk.input
          }, token);
          
          console.log(`✅ Tool ${chunk.name} executed successfully`);
          stepResult += `\n🔧 Used tool: ${chunk.name} - Success`;
          
          // Add tool result if it has meaningful content
          if (toolResult && typeof toolResult === 'string') {
            stepResult += `\nResult: ${toolResult}`;
          } else if (toolResult && typeof toolResult === 'object' && 'content' in toolResult) {
            stepResult += `\nResult: ${toolResult.content}`;
          }

        } catch (toolError) {
          console.error(`❌ Error executing tool ${chunk.name}:`, toolError);
          stepResult += `\n❌ Tool error (${chunk.name}): ${toolError instanceof Error ? toolError.message : String(toolError)}`;
          
          // Check if this is still the "Invalid stream" error
          if (toolError instanceof Error && toolError.message.includes('Invalid stream')) {
            stepResult += `\n⚠️ Note: This may indicate a VS Code API limitation`;
          }
        }
      }
    }

    if (toolsUsed.length > 0) {
      stepResult += `\n\nTools used: ${toolsUsed.join(', ')}`;
    }

    console.log('✅ Step execution completed');
    return stepResult || 'Step completed successfully';
  }
}
